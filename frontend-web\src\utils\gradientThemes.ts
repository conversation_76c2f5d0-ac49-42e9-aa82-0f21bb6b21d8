export interface GradientTheme {
  name: string;
  primary: string;
  primaryColor: string; // Solid color for MUI components
  secondary: string;
  secondaryColor: string; // Solid color for MUI components
  accent: string;
  accentColor: string; // Solid color for MUI components
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
}

export const gradientThemes: Record<string, GradientTheme> = {
  emerald: {
    name: 'Emerald Life',
    primary: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 30%, #4CAF50 70%, #66BB6A 100%)',
    primaryColor: '#4CAF50', // Main green color for MUI components
    secondary: 'linear-gradient(135deg, #388E3C 0%, #4CAF50 50%, #81C784 100%)',
    secondaryColor: '#66BB6A', // Secondary green for MUI components
    accent: 'linear-gradient(135deg, #81C784 0%, #A5D6A7 50%, #C8E6C9 100%)',
    accentColor: '#81C784', // Accent green for MUI components
    background: 'linear-gradient(135deg, rgba(27, 94, 32, 0.95) 0%, rgba(46, 125, 50, 0.9) 20%, rgba(56, 142, 60, 0.85) 40%, rgba(76, 175, 80, 0.8) 60%, rgba(102, 187, 106, 0.75) 80%, rgba(129, 199, 132, 0.7) 100%)',
    surface: 'rgba(76, 175, 80, 0.12)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.85)'
  },
  ocean: {
    name: 'Ocean Blue',
    primary: 'linear-gradient(135deg, #0D47A1 0%, #1565C0 30%, #1976D2 70%, #42A5F5 100%)',
    primaryColor: '#1976D2', // Main blue color for MUI components
    secondary: 'linear-gradient(135deg, #1976D2 0%, #2196F3 50%, #64B5F6 100%)',
    secondaryColor: '#42A5F5', // Secondary blue for MUI components
    accent: 'linear-gradient(135deg, #64B5F6 0%, #90CAF9 50%, #BBDEFB 100%)',
    accentColor: '#64B5F6', // Accent blue for MUI components
    background: 'linear-gradient(135deg, rgba(13, 71, 161, 0.95) 0%, rgba(21, 101, 192, 0.9) 20%, rgba(25, 118, 210, 0.85) 40%, rgba(33, 150, 243, 0.8) 60%, rgba(66, 165, 245, 0.75) 80%, rgba(100, 181, 246, 0.7) 100%)',
    surface: 'rgba(33, 150, 243, 0.12)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.85)'
  },
  sunset: {
    name: 'Warm Sunset',
    primary: 'linear-gradient(135deg, #E65100 0%, #F57C00 30%, #FF9800 70%, #FFB74D 100%)',
    primaryColor: '#FF9800', // Main orange color for MUI components
    secondary: 'linear-gradient(135deg, #FF9800 0%, #FFB74D 50%, #FFCC02 100%)',
    secondaryColor: '#FFB74D', // Secondary orange for MUI components
    accent: 'linear-gradient(135deg, #FFCC02 0%, #FFE082 50%, #FFF3C4 100%)',
    accentColor: '#FFCC02', // Accent yellow for MUI components
    background: 'linear-gradient(135deg, rgba(230, 81, 0, 0.95) 0%, rgba(245, 124, 0, 0.9) 20%, rgba(255, 152, 0, 0.85) 40%, rgba(255, 183, 77, 0.8) 60%, rgba(255, 204, 2, 0.75) 80%, rgba(255, 224, 130, 0.7) 100%)',
    surface: 'rgba(255, 152, 0, 0.12)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.85)'
  },
  purple: {
    name: 'Royal Purple',
    primary: 'linear-gradient(135deg, #4A148C 0%, #6A1B9A 30%, #8E24AA 70%, #AB47BC 100%)',
    primaryColor: '#8E24AA', // Main purple color for MUI components
    secondary: 'linear-gradient(135deg, #8E24AA 0%, #AB47BC 50%, #CE93D8 100%)',
    secondaryColor: '#AB47BC', // Secondary purple for MUI components
    accent: 'linear-gradient(135deg, #CE93D8 0%, #E1BEE7 50%, #F3E5F5 100%)',
    accentColor: '#CE93D8', // Accent purple for MUI components
    background: 'linear-gradient(135deg, rgba(74, 20, 140, 0.95) 0%, rgba(106, 27, 154, 0.9) 20%, rgba(142, 36, 170, 0.85) 40%, rgba(171, 71, 188, 0.8) 60%, rgba(206, 147, 216, 0.75) 80%, rgba(225, 190, 231, 0.7) 100%)',
    surface: 'rgba(142, 36, 170, 0.12)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.85)'
  },
  forest: {
    name: 'Deep Forest',
    primary: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 50%, #388E3C 100%)',
    primaryColor: '#388E3C', // Main forest green color for MUI components
    secondary: 'linear-gradient(135deg, #388E3C 0%, #4CAF50 100%)',
    secondaryColor: '#4CAF50', // Secondary forest green for MUI components
    accent: 'linear-gradient(135deg, #66BB6A 0%, #81C784 100%)',
    accentColor: '#66BB6A', // Accent forest green for MUI components
    background: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 30%, #388E3C 60%, #4CAF50 100%)',
    surface: 'rgba(46, 125, 50, 0.15)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)'
  },
  crimson: {
    name: 'Crimson Fire',
    primary: 'linear-gradient(135deg, #B71C1C 0%, #D32F2F 50%, #F44336 100%)',
    primaryColor: '#F44336', // Main red color for MUI components
    secondary: 'linear-gradient(135deg, #F44336 0%, #EF5350 100%)',
    secondaryColor: '#EF5350', // Secondary red for MUI components
    accent: 'linear-gradient(135deg, #E57373 0%, #FFCDD2 100%)',
    accentColor: '#E57373', // Accent red for MUI components
    background: 'linear-gradient(135deg, #B71C1C 0%, #D32F2F 25%, #F44336 50%, #EF5350 75%, #E57373 100%)',
    surface: 'rgba(244, 67, 54, 0.15)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)'
  }
};

export const getThemeByName = (name: string): GradientTheme => {
  return gradientThemes[name] || gradientThemes.emerald;
};

export const getAllThemeNames = (): string[] => {
  return Object.keys(gradientThemes);
};

export const getRandomTheme = (): GradientTheme => {
  const names = getAllThemeNames();
  const randomName = names[Math.floor(Math.random() * names.length)];
  return gradientThemes[randomName];
};
