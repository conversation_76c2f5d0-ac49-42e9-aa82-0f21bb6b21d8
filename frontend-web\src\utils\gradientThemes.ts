export interface GradientTheme {
  name: string;
  primary: string;
  primaryColor: string; // Solid color for MUI components
  secondary: string;
  secondaryColor: string; // Solid color for MUI components
  accent: string;
  accentColor: string; // Solid color for MUI components
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
}

export const gradientThemes: Record<string, GradientTheme> = {
  emerald: {
    name: 'Emerald Life',
    primary: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 50%, #4CAF50 100%)',
    primaryColor: '#4CAF50', // Main green color for MUI components
    secondary: 'linear-gradient(135deg, #388E3C 0%, #66BB6A 100%)',
    secondaryColor: '#66BB6A', // Secondary green for MUI components
    accent: 'linear-gradient(135deg, #81C784 0%, #A5D6A7 100%)',
    accentColor: '#81C784', // Accent green for MUI components
    background: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 25%, #388E3C 50%, #4CAF50 75%, #66BB6A 100%)',
    surface: 'rgba(76, 175, 80, 0.15)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)'
  },
  ocean: {
    name: 'Ocean Blue',
    primary: 'linear-gradient(135deg, #0D47A1 0%, #1565C0 50%, #1976D2 100%)',
    primaryColor: '#1976D2', // Main blue color for MUI components
    secondary: 'linear-gradient(135deg, #1976D2 0%, #42A5F5 100%)',
    secondaryColor: '#42A5F5', // Secondary blue for MUI components
    accent: 'linear-gradient(135deg, #64B5F6 0%, #90CAF9 100%)',
    accentColor: '#64B5F6', // Accent blue for MUI components
    background: 'linear-gradient(135deg, #0D47A1 0%, #1565C0 25%, #1976D2 50%, #42A5F5 75%, #64B5F6 100%)',
    surface: 'rgba(33, 150, 243, 0.15)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)'
  },
  sunset: {
    name: 'Warm Sunset',
    primary: 'linear-gradient(135deg, #E65100 0%, #F57C00 50%, #FF9800 100%)',
    primaryColor: '#FF9800', // Main orange color for MUI components
    secondary: 'linear-gradient(135deg, #FF9800 0%, #FFB74D 100%)',
    secondaryColor: '#FFB74D', // Secondary orange for MUI components
    accent: 'linear-gradient(135deg, #FFCC02 0%, #FFE082 100%)',
    accentColor: '#FFCC02', // Accent yellow for MUI components
    background: 'linear-gradient(135deg, #E65100 0%, #F57C00 25%, #FF9800 50%, #FFB74D 75%, #FFCC02 100%)',
    surface: 'rgba(255, 152, 0, 0.15)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)'
  },
  purple: {
    name: 'Royal Purple',
    primary: 'linear-gradient(135deg, #4A148C 0%, #6A1B9A 50%, #8E24AA 100%)',
    primaryColor: '#8E24AA', // Main purple color for MUI components
    secondary: 'linear-gradient(135deg, #8E24AA 0%, #AB47BC 100%)',
    secondaryColor: '#AB47BC', // Secondary purple for MUI components
    accent: 'linear-gradient(135deg, #CE93D8 0%, #E1BEE7 100%)',
    accentColor: '#CE93D8', // Accent purple for MUI components
    background: 'linear-gradient(135deg, #4A148C 0%, #6A1B9A 25%, #8E24AA 50%, #AB47BC 75%, #CE93D8 100%)',
    surface: 'rgba(142, 36, 170, 0.15)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)'
  },
  forest: {
    name: 'Deep Forest',
    primary: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 50%, #388E3C 100%)',
    primaryColor: '#388E3C', // Main forest green color for MUI components
    secondary: 'linear-gradient(135deg, #388E3C 0%, #4CAF50 100%)',
    secondaryColor: '#4CAF50', // Secondary forest green for MUI components
    accent: 'linear-gradient(135deg, #66BB6A 0%, #81C784 100%)',
    accentColor: '#66BB6A', // Accent forest green for MUI components
    background: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 30%, #388E3C 60%, #4CAF50 100%)',
    surface: 'rgba(46, 125, 50, 0.15)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)'
  },
  crimson: {
    name: 'Crimson Fire',
    primary: 'linear-gradient(135deg, #B71C1C 0%, #D32F2F 50%, #F44336 100%)',
    primaryColor: '#F44336', // Main red color for MUI components
    secondary: 'linear-gradient(135deg, #F44336 0%, #EF5350 100%)',
    secondaryColor: '#EF5350', // Secondary red for MUI components
    accent: 'linear-gradient(135deg, #E57373 0%, #FFCDD2 100%)',
    accentColor: '#E57373', // Accent red for MUI components
    background: 'linear-gradient(135deg, #B71C1C 0%, #D32F2F 25%, #F44336 50%, #EF5350 75%, #E57373 100%)',
    surface: 'rgba(244, 67, 54, 0.15)',
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)'
  }
};

export const getThemeByName = (name: string): GradientTheme => {
  return gradientThemes[name] || gradientThemes.emerald;
};

export const getAllThemeNames = (): string[] => {
  return Object.keys(gradientThemes);
};

export const getRandomTheme = (): GradientTheme => {
  const names = getAllThemeNames();
  const randomName = names[Math.floor(Math.random() * names.length)];
  return gradientThemes[randomName];
};
