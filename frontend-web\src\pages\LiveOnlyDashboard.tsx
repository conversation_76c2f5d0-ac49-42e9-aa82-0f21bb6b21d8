import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem,
  useTheme,
  alpha
} from '@mui/material';
import {
  Agriculture,
  Pets,
  LocalHospital,
  TrendingUp,
  AccountBalance,
  Inventory,
  Business,
  Assessment,
  Settings,
  Gavel,
  Dashboard as DashboardIcon,
  RestaurantMenu,
  Language,
  Palette,
  Logout,
  Star
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { gradientThemes } from '../utils/gradientThemes';

const LiveOnlyDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const theme = useTheme();
  const [selectedTheme, setSelectedTheme] = useState('emerald');
  const [themeAnchor, setThemeAnchor] = useState<null | HTMLElement>(null);

  const currentTheme = gradientThemes[selectedTheme];

  // ALL modules available for Live users
  const liveModules = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: <DashboardIcon />,
      description: 'Complete farm overview and analytics',
      route: '/dashboard',
      color: '#2196F3',
      tier: 'All Plans'
    },
    {
      id: 'animals',
      name: 'Animal Management',
      icon: <Pets />,
      description: 'Unlimited animal tracking and management',
      route: '/dashboard/animals',
      color: '#4CAF50',
      tier: 'All Plans'
    },
    {
      id: 'health',
      name: 'Health Management',
      icon: <LocalHospital />,
      description: 'Advanced health monitoring and veterinary records',
      route: '/dashboard/health',
      color: '#F44336',
      tier: 'All Plans'
    },
    {
      id: 'breeding',
      name: 'Breeding Management',
      icon: <TrendingUp />,
      description: 'Advanced breeding analytics and records',
      route: '/dashboard/breeding',
      color: '#9C27B0',
      tier: 'Professional+'
    },
    {
      id: 'feeding',
      name: 'Feeding Management',
      icon: <RestaurantMenu />,
      description: 'Smart feeding schedules and nutrition tracking',
      route: '/dashboard/feeding',
      color: '#FF9800',
      tier: 'All Plans'
    },
    {
      id: 'financial',
      name: 'Financial Management',
      icon: <AccountBalance />,
      description: 'Complete financial tracking and profitability analysis',
      route: '/dashboard/financial',
      color: '#795548',
      tier: 'Professional+'
    },
    {
      id: 'inventory',
      name: 'Inventory Management',
      icon: <Inventory />,
      description: 'Smart inventory with auto-reordering',
      route: '/dashboard/inventory',
      color: '#607D8B',
      tier: 'Professional+'
    },
    {
      id: 'commercial',
      name: 'Commercial Operations',
      icon: <Business />,
      description: 'Sales, marketing, and business operations',
      route: '/dashboard/commercial',
      color: '#3F51B5',
      tier: 'Enterprise'
    },
    {
      id: 'analytics',
      name: 'AI Analytics',
      icon: <Assessment />,
      description: 'Predictive insights and recommendations',
      route: '/dashboard/analytics',
      color: '#E91E63',
      tier: 'Professional+'
    },
    {
      id: 'compliance',
      name: 'Compliance',
      icon: <Gavel />,
      description: 'Regulatory compliance and documentation',
      route: '/dashboard/compliance',
      color: '#009688',
      tier: 'Enterprise'
    },
    {
      id: 'reports',
      name: 'Reports',
      icon: <Assessment />,
      description: 'Comprehensive reporting and insights',
      route: '/dashboard/reports',
      color: '#FF5722',
      tier: 'All Plans'
    },
    {
      id: 'settings',
      name: 'Settings',
      icon: <Settings />,
      description: 'System configuration and preferences',
      route: '/dashboard/settings',
      color: '#9E9E9E',
      tier: 'All Plans'
    }
  ];

  const handleModuleClick = (module: any) => {
    navigate(module.route);
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const getUserTier = () => {
    if (user?.role === 'admin' || user?.username === 'May Rakgama') return 'Administrator';
    if (user?.role === 'enterprise') return 'Enterprise';
    if (user?.role === 'professional') return 'Professional';
    return 'Basic';
  };

  const canAccessModule = (module: any) => {
    const userTier = getUserTier();
    if (userTier === 'Administrator') return true;
    if (module.tier === 'All Plans') return true;
    if (module.tier === 'Professional+' && ['Professional', 'Enterprise'].includes(userTier)) return true;
    if (module.tier === 'Enterprise' && userTier === 'Enterprise') return true;
    return false;
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: currentTheme.background,
        position: 'relative'
      }}
    >
      {/* Live Header */}
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          background: alpha(currentTheme.primaryColor, 0.95),
          backdropFilter: 'blur(20px)'
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Agriculture sx={{ fontSize: 40, color: '#fff' }} />
            <Box>
              <Typography variant="h5" fontWeight="bold" color="white">
                AgriIntel Live
              </Typography>
              <Typography variant="caption" color="rgba(255,255,255,0.8)">
                Welcome, {user?.username || 'User'} • {getUserTier()}
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Chip
              label={`${getUserTier().toUpperCase()} ACCESS`}
              size="small"
              sx={{
                background: getUserTier() === 'Administrator' ? '#4CAF50' : 
                           getUserTier() === 'Enterprise' ? '#9C27B0' :
                           getUserTier() === 'Professional' ? '#2196F3' : '#FF9800',
                color: '#fff',
                fontWeight: 'bold'
              }}
            />
            
            <IconButton
              onClick={(e) => setThemeAnchor(e.currentTarget)}
              sx={{ color: 'white' }}
            >
              <Palette />
            </IconButton>
            
            <IconButton onClick={handleLogout} sx={{ color: 'white' }}>
              <Logout />
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Theme Selector Menu */}
      <Menu
        anchorEl={themeAnchor}
        open={Boolean(themeAnchor)}
        onClose={() => setThemeAnchor(null)}
      >
        {Object.entries(gradientThemes).map(([name, themeData]) => (
          <MenuItem
            key={name}
            onClick={() => {
              setSelectedTheme(name);
              setThemeAnchor(null);
            }}
            sx={{
              background: selectedTheme === name ? alpha(themeData.primaryColor, 0.1) : 'transparent'
            }}
          >
            <Box
              sx={{
                width: 20,
                height: 20,
                borderRadius: '50%',
                background: themeData.primaryColor,
                mr: 2
              }}
            />
            {name.charAt(0).toUpperCase() + name.slice(1)}
          </MenuItem>
        ))}
      </Menu>

      <Container maxWidth="xl" sx={{ pt: 12, pb: 4 }}>
        {/* Live Welcome Section */}
        <Typography
          variant="h3"
          sx={{
            color: 'white',
            mb: 6,
            fontWeight: 'bold',
            textAlign: 'center'
          }}
        >
          Complete Livestock Management Suite
        </Typography>

        {/* Live Modules Grid */}
        <Grid container spacing={3}>
          {liveModules.map((module, index) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={module.id}>
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.05 }}
                whileHover={{ scale: canAccessModule(module) ? 1.05 : 1, y: canAccessModule(module) ? -8 : 0 }}
              >
                <Card
                  onClick={() => canAccessModule(module) && handleModuleClick(module)}
                  sx={{
                    height: 220,
                    background: canAccessModule(module) 
                      ? alpha('#fff', 0.1) 
                      : alpha('#000', 0.3),
                    backdropFilter: 'blur(20px)',
                    border: `1px solid ${alpha('#fff', canAccessModule(module) ? 0.2 : 0.1)}`,
                    borderRadius: 3,
                    cursor: canAccessModule(module) ? 'pointer' : 'not-allowed',
                    transition: 'all 0.3s ease',
                    position: 'relative',
                    opacity: canAccessModule(module) ? 1 : 0.6,
                    '&:hover': canAccessModule(module) ? {
                      background: alpha('#fff', 0.15),
                      transform: 'translateY(-8px)'
                    } : {}
                  }}
                >
                  {!canAccessModule(module) && (
                    <Chip
                      label={module.tier}
                      size="small"
                      sx={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        background: '#FF9800',
                        color: '#fff',
                        fontWeight: 'bold',
                        fontSize: '0.7rem'
                      }}
                    />
                  )}
                  
                  <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        borderRadius: '50%',
                        background: canAccessModule(module) ? module.color : alpha(module.color, 0.5),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                        color: 'white'
                      }}
                    >
                      {React.cloneElement(module.icon, { fontSize: 'medium' })}
                    </Box>
                    
                    <Typography
                      variant="h6"
                      sx={{ 
                        color: 'white', 
                        mb: 1, 
                        fontWeight: 'bold',
                        fontSize: '1rem'
                      }}
                    >
                      {module.name}
                    </Typography>
                    
                    <Typography
                      variant="body2"
                      sx={{ 
                        color: alpha('#fff', 0.8), 
                        lineHeight: 1.4,
                        fontSize: '0.8rem'
                      }}
                    >
                      {module.description}
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

export default LiveOnlyDashboard;
