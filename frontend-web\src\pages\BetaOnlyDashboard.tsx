import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  useTheme,
  alpha
} from '@mui/material';
import {
  Agriculture,
  Pets,
  LocalHospital,
  Visibility,
  Dashboard as DashboardIcon,
  TrendingUp,
  AccountBalance,
  Inventory,
  Business,
  Assessment,
  Gavel,
  RestaurantMenu,
  Settings,
  Language,
  Palette,
  Logout,
  Upgrade,
  Star,
  Lock
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { gradientThemes } from '../utils/gradientThemes';

const BetaOnlyDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const theme = useTheme();
  const [selectedTheme, setSelectedTheme] = useState('emerald');
  const [themeAnchor, setThemeAnchor] = useState<null | HTMLElement>(null);

  const currentTheme = gradientThemes[selectedTheme];

  // ALL modules with BETA access control
  const betaModules = [
    {
      id: 'dashboard',
      name: 'Dashboard Overview',
      icon: <DashboardIcon />,
      description: 'View your farm overview and basic statistics',
      route: '/beta-dashboard',
      color: '#2196F3',
      available: true,
      tier: 'BETA'
    },
    {
      id: 'animals',
      name: 'Animal Management',
      icon: <Pets />,
      description: 'Manage up to 50 animals (BETA limit)',
      route: '/beta-dashboard/animals',
      color: '#4CAF50',
      available: true,
      tier: 'BETA'
    },
    {
      id: 'health',
      name: 'Health Monitoring',
      icon: <LocalHospital />,
      description: 'Basic health records and monitoring',
      route: '/beta-dashboard/health',
      color: '#F44336',
      available: true,
      tier: 'BETA'
    },
    {
      id: 'resources',
      name: 'Resources & Info',
      icon: <Visibility />,
      description: 'Government resources and information',
      route: '/beta-dashboard/resources',
      color: '#00BCD4',
      available: true,
      tier: 'BETA'
    },
    {
      id: 'breeding',
      name: 'Breeding Management',
      icon: <TrendingUp />,
      description: 'Advanced breeding analytics & records',
      route: '/beta-dashboard/breeding',
      color: '#9C27B0',
      available: false,
      tier: 'PROFESSIONAL'
    },
    {
      id: 'feeding',
      name: 'Feeding Management',
      icon: <RestaurantMenu />,
      description: 'Smart feeding schedules and nutrition tracking',
      route: '/beta-dashboard/feeding',
      color: '#FF9800',
      available: false,
      tier: 'PROFESSIONAL'
    },
    {
      id: 'financial',
      name: 'Financial Management',
      icon: <AccountBalance />,
      description: 'Complete financial tracking & profitability analysis',
      route: '/beta-dashboard/financial',
      color: '#795548',
      available: false,
      tier: 'PROFESSIONAL'
    },
    {
      id: 'inventory',
      name: 'Inventory Management',
      icon: <Inventory />,
      description: 'Smart inventory with auto-reordering',
      route: '/beta-dashboard/inventory',
      color: '#607D8B',
      available: false,
      tier: 'PROFESSIONAL'
    },
    {
      id: 'commercial',
      name: 'Commercial Operations',
      icon: <Business />,
      description: 'Sales, marketing, and business operations',
      route: '/beta-dashboard/commercial',
      color: '#3F51B5',
      available: false,
      tier: 'ENTERPRISE'
    },
    {
      id: 'analytics',
      name: 'AI Analytics',
      icon: <Assessment />,
      description: 'Predictive insights and recommendations',
      route: '/beta-dashboard/analytics',
      color: '#E91E63',
      available: false,
      tier: 'PROFESSIONAL'
    },
    {
      id: 'compliance',
      name: 'Compliance',
      icon: <Gavel />,
      description: 'Regulatory compliance and documentation',
      route: '/beta-dashboard/compliance',
      color: '#009688',
      available: false,
      tier: 'ENTERPRISE'
    },
    {
      id: 'settings',
      name: 'Settings',
      icon: <Settings />,
      description: 'System configuration and preferences',
      route: '/beta-dashboard/settings',
      color: '#9E9E9E',
      available: true,
      tier: 'BETA'
    }
  ];

  const upgradeFeatures = [
    'Unlimited Animals',
    'Advanced Analytics',
    'Financial Management',
    'Breeding Management',
    'Inventory Control',
    'Commercial Features',
    'Priority Support',
    'API Access'
  ];

  const handleModuleClick = (module: any) => {
    if (module.available) {
      navigate(module.route);
    } else {
      // Show upgrade prompt for locked modules
      handleUpgrade();
    }
  };

  const handleUpgrade = () => {
    navigate('/login'); // Redirect to live login for upgrade
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: currentTheme.background,
        position: 'relative'
      }}
    >
      {/* BETA Header */}
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          background: alpha(currentTheme.primaryColor, 0.95),
          backdropFilter: 'blur(20px)'
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Agriculture sx={{ fontSize: 40, color: '#fff' }} />
            <Box>
              <Typography variant="h5" fontWeight="bold" color="white">
                AgriIntel BETA
              </Typography>
              <Typography variant="caption" color="rgba(255,255,255,0.8)">
                Welcome, {user?.username || 'Demo User'}
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Chip
              label="BETA ACCESS"
              size="small"
              sx={{
                background: '#FFC107',
                color: '#000',
                fontWeight: 'bold'
              }}
            />
            
            <IconButton
              onClick={(e) => setThemeAnchor(e.currentTarget)}
              sx={{ color: 'white' }}
            >
              <Palette />
            </IconButton>
            
            <Button
              variant="outlined"
              startIcon={<Upgrade />}
              onClick={handleUpgrade}
              sx={{
                color: 'white',
                borderColor: 'white',
                '&:hover': {
                  borderColor: 'white',
                  background: alpha('#fff', 0.1)
                }
              }}
            >
              Upgrade
            </Button>
            
            <IconButton onClick={handleLogout} sx={{ color: 'white' }}>
              <Logout />
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Theme Selector Menu */}
      <Menu
        anchorEl={themeAnchor}
        open={Boolean(themeAnchor)}
        onClose={() => setThemeAnchor(null)}
      >
        {Object.entries(gradientThemes).map(([name, themeData]) => (
          <MenuItem
            key={name}
            onClick={() => {
              setSelectedTheme(name);
              setThemeAnchor(null);
            }}
            sx={{
              background: selectedTheme === name ? alpha(themeData.primaryColor, 0.1) : 'transparent'
            }}
          >
            <Box
              sx={{
                width: 20,
                height: 20,
                borderRadius: '50%',
                background: themeData.primaryColor,
                mr: 2
              }}
            />
            {name.charAt(0).toUpperCase() + name.slice(1)}
          </MenuItem>
        ))}
      </Menu>

      <Container maxWidth="lg" sx={{ pt: 12, pb: 4 }}>
        {/* BETA Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Alert 
            severity="info" 
            sx={{ 
              mb: 4,
              background: alpha('#2196F3', 0.1),
              border: `1px solid ${alpha('#2196F3', 0.3)}`
            }}
          >
            <Typography variant="h6" fontWeight="bold">
              🎉 Welcome to AgriIntel BETA!
            </Typography>
            <Typography variant="body2">
              You have access to core livestock management features. Upgrade to Professional or Enterprise for advanced features.
            </Typography>
          </Alert>
        </motion.div>

        {/* BETA Modules Grid */}
        <Typography
          variant="h3"
          sx={{
            color: 'white',
            mb: 4,
            fontWeight: 'bold',
            textAlign: 'center'
          }}
        >
          Your BETA Features
        </Typography>

        <Grid container spacing={3} sx={{ mb: 6 }}>
          {betaModules.map((module, index) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={module.id}>
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.05 }}
                whileHover={{ scale: module.available ? 1.05 : 1, y: module.available ? -8 : 0 }}
              >
                <Card
                  onClick={() => handleModuleClick(module)}
                  sx={{
                    height: 220,
                    background: module.available
                      ? alpha('#fff', 0.1)
                      : alpha('#000', 0.3),
                    backdropFilter: 'blur(20px)',
                    border: `1px solid ${alpha('#fff', module.available ? 0.2 : 0.1)}`,
                    borderRadius: 3,
                    cursor: module.available ? 'pointer' : 'not-allowed',
                    transition: 'all 0.3s ease',
                    position: 'relative',
                    opacity: module.available ? 1 : 0.6,
                    '&:hover': module.available ? {
                      background: alpha('#fff', 0.15),
                      transform: 'translateY(-8px)'
                    } : {}
                  }}
                >
                  {/* Lock indicator for premium modules */}
                  {!module.available && (
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        zIndex: 2
                      }}
                    >
                      <Chip
                        icon={<Lock />}
                        label={module.tier}
                        size="small"
                        sx={{
                          background: module.tier === 'PROFESSIONAL' ? '#FF9800' : '#9C27B0',
                          color: '#fff',
                          fontWeight: 'bold',
                          fontSize: '0.7rem'
                        }}
                      />
                    </Box>
                  )}

                  {/* Available indicator for BETA modules */}
                  {module.available && module.tier === 'BETA' && (
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        zIndex: 2
                      }}
                    >
                      <Chip
                        label="FREE"
                        size="small"
                        sx={{
                          background: '#4CAF50',
                          color: '#fff',
                          fontWeight: 'bold',
                          fontSize: '0.7rem'
                        }}
                      />
                    </Box>
                  )}

                  <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        borderRadius: '50%',
                        background: module.available ? module.color : alpha(module.color, 0.5),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                        color: 'white',
                        position: 'relative'
                      }}
                    >
                      {React.cloneElement(module.icon, { fontSize: 'medium' })}
                      {!module.available && (
                        <Lock
                          sx={{
                            position: 'absolute',
                            bottom: -5,
                            right: -5,
                            fontSize: 20,
                            background: '#FF5722',
                            borderRadius: '50%',
                            p: 0.5
                          }}
                        />
                      )}
                    </Box>

                    <Typography
                      variant="h6"
                      sx={{
                        color: 'white',
                        mb: 1,
                        fontWeight: 'bold',
                        fontSize: '1rem'
                      }}
                    >
                      {module.name}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{
                        color: alpha('#fff', 0.8),
                        lineHeight: 1.4,
                        fontSize: '0.8rem'
                      }}
                    >
                      {module.description}
                    </Typography>

                    {!module.available && (
                      <Button
                        size="small"
                        startIcon={<Upgrade />}
                        sx={{
                          mt: 1,
                          color: '#FFC107',
                          borderColor: '#FFC107',
                          fontSize: '0.7rem',
                          '&:hover': {
                            borderColor: '#FFB300',
                            background: alpha('#FFC107', 0.1)
                          }
                        }}
                        variant="outlined"
                      >
                        Upgrade
                      </Button>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Upgrade Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <Card
            sx={{
              background: alpha('#fff', 0.1),
              backdropFilter: 'blur(20px)',
              border: `2px solid ${alpha('#FFC107', 0.5)}`,
              borderRadius: 3,
              p: 4,
              textAlign: 'center'
            }}
          >
            <Star sx={{ fontSize: 60, color: '#FFC107', mb: 2 }} />
            
            <Typography variant="h4" sx={{ color: 'white', mb: 2, fontWeight: 'bold' }}>
              Unlock Premium Features
            </Typography>
            
            <Typography variant="h6" sx={{ color: alpha('#fff', 0.9), mb: 3 }}>
              Upgrade to Professional (R299/month) or Enterprise (R599/month)
            </Typography>

            <Grid container spacing={2} sx={{ mb: 4 }}>
              {upgradeFeatures.map((feature, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Typography
                    variant="body2"
                    sx={{
                      color: alpha('#fff', 0.8),
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <Star sx={{ fontSize: 16, color: '#FFC107' }} />
                    {feature}
                  </Typography>
                </Grid>
              ))}
            </Grid>

            <Button
              variant="contained"
              size="large"
              startIcon={<Upgrade />}
              onClick={handleUpgrade}
              sx={{
                background: '#FFC107',
                color: '#000',
                px: 4,
                py: 2,
                fontSize: '1.2rem',
                fontWeight: 'bold',
                '&:hover': {
                  background: '#FFB300'
                }
              }}
            >
              Upgrade Now
            </Button>
          </Card>
        </motion.div>
      </Container>
    </Box>
  );
};

export default BetaOnlyDashboard;
